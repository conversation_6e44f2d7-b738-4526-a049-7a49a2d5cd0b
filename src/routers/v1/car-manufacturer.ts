import { <PERSON><PERSON>Router } from '../crud';
import { carManufacturerController } from '@/controllers';
import { 
    authInfoMiddleware,
    adminTypeMiddleware 
} from '@/middlewares';

export default class CarManufacturer<PERSON>outer extends <PERSON><PERSON>Router<typeof carManufacturerController> {
    constructor() {
        super(carManufacturerController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
} 