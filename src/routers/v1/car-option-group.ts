import { <PERSON>rudRouter } from '../crud';
import { carOptionGroupController } from '@/controllers';
import { 
    authInfoMiddleware,
    adminTypeMiddleware 
} from '@/middlewares';

export default class CarOptionGroupRouter extends C<PERSON>Router<typeof carOptionGroupController> {
    constructor() {
        super(carOptionGroupController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
