import { <PERSON>rudRouter } from '../crud';
import { carOptionController } from '@/controllers';
import { 
    authInfoMiddleware,
    adminTypeMiddleware 
} from '@/middlewares';

export default class CarOptionRouter extends C<PERSON>Router<typeof carOptionController> {
    constructor() {
        super(carOptionController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
