import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { carOptionController } from '@/controllers';
import { Request, Response } from '../base';
import {
    authInfoMiddleware,
    adminTypeMiddleware,
    queryMiddleware
} from '@/middlewares';

export default class Car<PERSON><PERSON>Router extends <PERSON><PERSON><PERSON>outer<typeof carOptionController> {
    constructor() {
        super(carOptionController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run(), queryMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run(), queryMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    // Override getItem to handle queryInfo initialization
    async getItem(req: Request, res: Response) {
        const { id } = req.params;

        // Initialize queryInfo if it's undefined (fallback for middleware issues)
        if (!req.queryInfo) {
            req.queryInfo = {
                filter: {},
                limit: 20,
                page: 1,
                offset: 0,
                order: [['updated_at', 'desc']]
            };
        }

        req.queryInfo.filter.id = id;
        const result = await this.controller.getItem(req.queryInfo);
        this.onSuccess(res, result);
    }
}
