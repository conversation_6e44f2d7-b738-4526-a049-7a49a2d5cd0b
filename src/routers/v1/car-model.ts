import { <PERSON><PERSON>Router } from '../crud';
import { carModelController } from '@/controllers';
import { 
    authInfoMiddleware,
    adminTypeMiddleware 
} from '@/middlewares';

export default class CarModelRouter extends <PERSON><PERSON>Router<typeof carModelController> {
    constructor() {
        super(carModelController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }
}
