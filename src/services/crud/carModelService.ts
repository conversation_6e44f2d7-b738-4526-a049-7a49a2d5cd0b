import { CrudService, ICrudOption } from '../crudService.pg';
import { CarModel } from '@/models/tables';
import { config } from '@/config';

export class CarModelService extends CrudService<typeof CarModel> {
  constructor() {
    super(CarModel);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'manufacturer',
            attributes: ['id', 'name', 'country']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'manufacturer',
            attributes: ['id', 'name', 'country']
          }
        ],
        where: option.filter
      })
    );
  }
}
