import { CrudService, ICrudOption } from '../crudService.pg';
import { CarOptionGroup } from '@/models/tables';
import { config } from '@/config';

export class CarOptionGroupService extends CrudService<typeof CarOptionGroup> {
  constructor() {
    super(CarOptionGroup);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'options',
            attributes: ['id', 'name', 'description']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['index', 'ASC'], ['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'options',
            attributes: ['id', 'name', 'description']
          }
        ],
        where: option.filter
      })
    );
  }
}
