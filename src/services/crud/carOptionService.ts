import { CrudService, ICrudOption } from '../crudService.pg';
import { CarOption } from '@/models/tables';
import { config } from '@/config';

export class CarOptionService extends CrudService<typeof CarOption> {
  constructor() {
    super(CarOption);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'option_group',
            attributes: ['id', 'name', 'description']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['index', 'ASC'], ['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'option_group',
            attributes: ['id', 'name', 'description']
          }
        ],
        where: option.filter
      })
    );
  }
}
